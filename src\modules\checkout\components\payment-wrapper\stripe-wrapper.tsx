"use client"

import { Stripe, StripeElementsOptions } from "@stripe/stripe-js"
import { Elements } from "@stripe/react-stripe-js"
import { HttpTypes } from "@medusajs/types"
import { createContext } from "react"

type StripeWrapperProps = {
  paymentSession: HttpTypes.StorePaymentSession
  stripeKey?: string
  stripePromise: Promise<Stripe | null> | null
  children: React.ReactNode
}

export const StripeContext = createContext(false)

const StripeWrapper: React.FC<StripeWrapperProps> = ({
  paymentSession,
  stripeKey,
  stripePromise,
  children,
}) => {
  const clientSecret = paymentSession!.data?.client_secret as string

  const options: StripeElementsOptions = {
    clientSecret: clientSecret,
    appearance: {
      theme: 'stripe',
    },
    loader: 'auto',
  }

  console.log('StripeWrapper options:', {
    clientSecret: clientSecret,
    hasClientSecret: !!clientSecret,
    clientSecretFormat: clientSecret?.substring(0, 10) + '...',
    isValidFormat: clientSecret?.startsWith('pi_') || clientSecret?.startsWith('seti_'),
    paymentSessionData: paymentSession!.data
  })

  if (!stripeKey) {
    throw new Error(
      "Stripe key is missing. Set NEXT_PUBLIC_STRIPE_KEY environment variable."
    )
  }

  if (!stripePromise) {
    throw new Error(
      "Stripe promise is missing. Make sure you have provided a valid Stripe key."
    )
  }

  if (!clientSecret) {
    throw new Error(
      "Stripe client secret is missing. Cannot initialize Stripe."
    )
  }

  if (!clientSecret.startsWith('pi_') && !clientSecret.startsWith('seti_')) {
    throw new Error(
      `Invalid Stripe client secret format: ${clientSecret.substring(0, 10)}... Expected format: pi_xxx_secret_xxx or seti_xxx_secret_xxx`
    )
  }

  return (
    <StripeContext.Provider value={true}>
      <Elements options={options} stripe={stripePromise}>
        {children}
      </Elements>
    </StripeContext.Provider>
  )
}

export default StripeWrapper
