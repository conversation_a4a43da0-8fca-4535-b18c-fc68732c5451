{"name": "medusa-next", "version": "1.0.3", "private": true, "author": "<PERSON><PERSON> <<EMAIL>> & <PERSON> <<EMAIL>> (https://www.medusajs.com)", "description": "Next.js Starter to be used with Medusa V2", "keywords": ["medusa-storefront"], "scripts": {"dev": "next dev --turbopack -p 8000", "build": "next build", "postbuild": "next-sitemap", "build:production": "NEXT_CONFIG=next.config.production.js next build", "start": "next start -p 8000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "check-env": "node check-env-variables.js", "pre-build": "npm run check-env && npm run type-check && npm run lint", "clean": "rm -rf .next out", "clean:build": "npm run clean && npm run build", "sitemap": "next-sitemap", "analyze": "ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^2.2.0", "@medusajs/js-sdk": "latest", "@medusajs/ui": "latest", "@radix-ui/react-accordion": "^1.2.1", "@stripe/react-stripe-js": "^1.7.2", "@stripe/stripe-js": "^1.29.0", "lodash": "^4.17.21", "next": "^15.3.1", "pg": "^8.11.3", "qs": "^6.12.1", "react": "19.0.0-rc-66855b96-20241106", "react-country-flag": "^3.1.0", "react-dom": "19.0.0-rc-66855b96-20241106", "server-only": "^0.0.1", "tailwindcss-radix": "^2.8.0", "webpack": "^5"}, "devDependencies": {"@babel/core": "^7.17.5", "@medusajs/types": "latest", "@medusajs/ui-preset": "latest", "@types/lodash": "^4.14.195", "@types/node": "17.0.21", "@types/pg": "^8.11.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-instantsearch-dom": "^6.12.3", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.3", "eslint": "^8.57.0", "eslint-config-next": "15.0.3", "next-sitemap": "^4.2.3", "postcss": "^8.4.8", "prettier": "^2.8.8", "tailwindcss": "^3.0.23", "typescript": "^5.3.2"}, "resolutions": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "overrides": {"react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106"}}